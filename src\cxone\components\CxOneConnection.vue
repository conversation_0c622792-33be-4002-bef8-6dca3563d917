<template>
  <div class="cxone-connection">
    <div class="cxone-status" :class="statusClass" @click="toggleConnection">
      <span class="status-indicator"></span>
      <span class="status-text">{{ statusText }}</span>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  computed,
  onMounted,
  onBeforeUnmount
} from "@vue/composition-api";
import { useCxOneController } from "../controllers/useCxOneController";
import { loggerInstance } from "@/common/Logger";

export default defineComponent({
  name: "CxOneConnection",
  props: {
    socketUrl: {
      type: String,
      required: true
    }
  },
  setup(props, { emit }) {
    // Initialize the CxOne controller
    const {
      state,
      socketController,
      initialize,
      processMessage
    } = useCxOneController(props.socketUrl);

    // Computed properties for UI
    const statusClass = computed(() => {
      return {
        "status-connected": socketController.state.status === "Connected",
        "status-connecting":
          socketController.state.status === "Connecting" ||
          socketController.state.status === "Reconnecting",
        "status-error": socketController.state.status === "Error",
        "status-disconnected": socketController.state.status === "Not Connected"
      };
    });

    const statusText = computed(() => {
      return `CxOne: ${socketController.state.status}`;
    });

    // Methods
    const toggleConnection = async () => {
      if (socketController.isConnected) {
        await socketController.stopConnection();
        loggerInstance.log("CxOne connection manually stopped");
      } else {
        await initialize();
        loggerInstance.log("CxOne connection manually started");
      }
    };

    // Lifecycle hooks
    onMounted(async () => {
      loggerInstance.log("CxOneConnection component mounted");
      await initialize();
    });

    onBeforeUnmount(() => {
      loggerInstance.log("CxOneConnection component unmounting");
      socketController.stopConnection();
    });

    return {
      state,
      statusClass,
      statusText,
      toggleConnection
    };
  }
});
</script>

<style scoped>
.cxone-connection {
  display: inline-flex;
  align-items: center;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
}

.cxone-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
}

.status-connected .status-indicator {
  background-color: #4caf50;
}

.status-connecting .status-indicator {
  background-color: #ff9800;
  animation: pulse 1.5s infinite;
}

.status-error .status-indicator {
  background-color: #f44336;
}

.status-disconnected .status-indicator {
  background-color: #9e9e9e;
}

@keyframes pulse {
  0% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.5;
  }
}
</style>
