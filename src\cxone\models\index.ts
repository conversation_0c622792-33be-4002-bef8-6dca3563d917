export interface CleoCxOneControllerInput {
  socketUrl: string;
}

export type CleoCxOneMessageType =
  | "NLP_MATCHED"
  | "NLP_UNMATCHED"
  | "NO_PDI"
  | "SMS_MATCHED";

/**
 * Represents a message from the CxOne system
 */
export interface CleoCxOneMessage {
  /** Unique ID for the call session */
  ContactID: string;
  /** ID of the CLEO matched case, if available */
  CaseID: string | null;
  /** Agent's email address (User Principal Name) */
  UserEmail: string;
  /** Classification of the message */
  Type: CleoCxOneMessageType;
  /** The phone number the patient is calling from */
  PhoneNumber: string;
}
