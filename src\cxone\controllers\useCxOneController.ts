import { reactive } from "@vue/composition-api";
import { loggerInstance } from "@/common/Logger";
import { CleoCxOneMessage, CleoCxOneMessageType } from "../models";
import { useCxOneSocketController } from "../socket/useCxOneSocketController";

export interface CxOneControllerState {
  /** Most likely the ID of the user in CxOne, this is the bit that can be
   * used to identify the user in CxOne and CLEO*/
  myUserIdentifier: string;
  lastMessage: CleoCxOneMessage | null;
  isProcessing: boolean;
}

export function useCxOneController(socketUrl: string) {
  // Create socket controller
  const socketController = useCxOneSocketController({ socketUrl });

  // State for business logic
  const state = reactive<CxOneControllerState>({
    myUserIdentifier: "",
    lastMessage: null,
    isProcessing: false,
  });

  function setMyUserIdentifier(identifier: string) {
    state.myUserIdentifier = identifier;
  }

  // Initialize socket event handlers
  function initializeSocketHandlers() {
    // Register for CxOne messages
    socketController.on("CxOneMessage", (message: CleoCxOneMessage) => {
      processMessage(message);
    });
  }

  // Process a message regardless of source
  function processMessage(message: CleoCxOneMessage) {
    state.isProcessing = true;
    state.lastMessage = message;

    loggerInstance.log("Processing CxOne message:", message);

    if (message.UserEmail !== state.myUserIdentifier) {
      loggerInstance.log("CxOne message is not for me, ignoring");
      return;
    }

    try {
      // Process based on message type
      switch (message.Type) {
        case "NLP_MATCHED":
          return handleNlpMatched(message);
          break;
        case "NLP_UNMATCHED":
          return handleNlpUnmatched(message);
          break;
        case "NO_PDI":
          return handleNoPdi(message);
          break;
        case "SMS_MATCHED":
          return handleSmsMatched(message);
          break;
        default:
          loggerInstance.log("Unknown message type:", message.Type);
      }
    } catch (error) {
      loggerInstance.log("Error processing CxOne message:", error);
    } finally {
      state.isProcessing = false;
    }
  }

  // Handlers for different message types
  function handleNlpMatched(message: CleoCxOneMessage) {
    loggerInstance.log("Handling NLP_MATCHED message", message);

    const callStatusValue = 1;

    if (message.CaseID) {
      // We can open this case directly
      /*
      window.CallControllerClient.OpenDocumentNewWindow(
        message.CaseID,
        null,
        null,
        callStatusValue.toString(),
        cleoCallSummary.CallService.Description &&
          cleoCallSummary.CallService.Description.length > 0
          ? cleoCallSummary.CallService.Description
          : "",
        this.convertToLegacyCleoCallSummary(cleoCallSummary)
      );
      */
    }
  }

  function handleNlpUnmatched(message: CleoCxOneMessage) {
    loggerInstance.log("Handling NLP_UNMATCHED message", message);
    // Implement business logic for NLP_UNMATCHED
  }

  function handleNoPdi(message: CleoCxOneMessage) {
    loggerInstance.log("Handling NO_PDI message", message);
    // Implement business logic for NO_PDI
  }

  function handleSmsMatched(message: CleoCxOneMessage) {
    loggerInstance.log("Handling SMS_MATCHED message", message);
    // Implement business logic for SMS_MATCHED
  }

  // Initialize and connect
  function initialize() {
    initializeSocketHandlers();
    // return socketController.startConnection();
  }

  return {
    state,
    socketController,

    initialize,
    setMyUserIdentifier,
    processMessage,
  };
}
