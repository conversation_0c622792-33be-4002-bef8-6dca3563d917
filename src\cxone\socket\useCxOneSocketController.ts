import * as signalR from "@microsoft/signalr";
import { reactive } from "@vue/composition-api";
import { loggerInstance } from "@/common/Logger";
import { LoginService } from "@/login/login-service";
import { CleoCxOneControllerInput, CleoCxOneMessage } from "../models";

export function useCxOneSocketController(input: CleoCxOneControllerInput) {
  const state = reactive({
    connection: null as signalR.HubConnection | null,
    status: "Not Connected" as
      | "Not Connected"
      | "Connecting"
      | "Connected"
      | "Error"
      | "Reconnecting",
    isTransitioning: false
  });

  // Create and configure the connection
  function createConnection() {
    const loginService = new LoginService();
    const connection = new signalR.HubConnectionBuilder()
      .withUrl(input.socketUrl, {
        skipNegotiation: true,
        transport: signalR.HttpTransportType.WebSockets,
        accessTokenFactory: () => loginService.getJwtAccessToken()
      })
      .withAutomaticReconnect([0, 2000, 10000, 30000]) // Retry with backoff
      .configureLogging(signalR.LogLevel.Information)
      .build();

    // Set up connection event handlers
    connection.onreconnecting(error => {
      state.status = "Reconnecting";
      loggerInstance.log("CxOne socket reconnecting:", error);
    });

    connection.onreconnected(connectionId => {
      state.status = "Connected";
      loggerInstance.log(
        "CxOne socket reconnected. ConnectionId:",
        connectionId
      );
    });

    connection.onclose(error => {
      state.status = "Not Connected";
      loggerInstance.log("CxOne socket connection closed:", error);
    });

    return connection;
  }

  // Start the connection
  async function startConnection() {
    if (state.isTransitioning || state.status === "Connected") return;

    state.isTransitioning = true;
    state.status = "Connecting";

    try {
      if (!state.connection) {
        state.connection = createConnection();
      }

      await state.connection.start();
      state.status = "Connected";
      loggerInstance.log("CxOne socket connected successfully");
    } catch (error) {
      state.status = "Error";
      // Enhance error logging with more details
      loggerInstance.log("CxOne socket connection failed:", error);

      // Log additional transport details if available
      if (error instanceof Error) {
        const errorDetails = {
          message: error.message,
          name: error.name,
          stack: error.stack
        };
        loggerInstance.log(
          "CxOne socket connection error details:",
          errorDetails
        );

        // Use common error reporting if available
        try {
          const { useLogServerMessage } = require("@/common/common-app");
          useLogServerMessage(
            error,
            {
              socketUrl: input.socketUrl
            },
            "CxOneSocketController"
          );
        } catch (e) {
          // Fallback if common-app module is not available
          loggerInstance.log("Failed to report error to server:", e);
        }
      }
    } finally {
      state.isTransitioning = false;
    }
  }

  // Stop the connection
  async function stopConnection() {
    if (!state.connection || state.isTransitioning) return;

    state.isTransitioning = true;

    try {
      await state.connection.stop();
      state.status = "Not Connected";
      loggerInstance.log("CxOne socket disconnected");
    } catch (error) {
      state.status = "Error";
      loggerInstance.log("CxOne socket disconnection error:", error);
    } finally {
      state.isTransitioning = false;
    }
  }

  // Register event handlers
  function on(eventName: string, callback: (...args: any[]) => void) {
    if (state.connection) {
      state.connection.on(eventName, callback);
    }
  }

  // Invoke a method on the hub
  async function invoke(methodName: string, ...args: any[]) {
    if (!state.connection || state.status !== "Connected") {
      throw new Error("Cannot invoke method: connection not established");
    }

    return await state.connection.invoke(methodName, ...args);
  }

  return {
    state,
    startConnection,
    stopConnection,
    on,
    invoke,
    get isConnected() {
      return state.status === "Connected";
    }
  };
}
