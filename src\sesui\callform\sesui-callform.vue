<template>
  <div>
    <input
      v-if="sesuiCallClient.state.ui.allowTelephoneEdit"
      v-model="sesuiCallClient.state.telephoneNumberToCall"
      :style="
        !sesuiCallClient.isTelephoneNumberValid.value
          ? 'background-color: #fdd5d5'
          : ''
      "
      v-on:change="onTelephoneNumberChanged"
    />
    <span class="adapter-button--separator"></span>
    <a
      href="#"
      class="sesui-callform--edit-button"
      v-on:click.prevent="clickEditTelephone"
      v-text="sesuiCallClient.state.ui.allowTelephoneEdit ? 'Hide' : 'Edit'"
    ></a>
    <span class="adapter-button--separator"></span>
    <!--    :disabled="!sesuiCallClient.isTelephoneNumberValid.value"-->
    <button
      :disabled="!sesuiCallClient.isTelephoneNumberValid.value"
      v-if="sesuiCallClient.state.ui.showMakeCall"
      class="adapter-button adapter-button--green sesui-callform--button"
      v-on:click.stop="sesuiCallClient.makeCall()"
    >
      Call
    </button>

    <!--    <button-->
    <!--      v-if="sesuiCallClient.state.ui.showMakeCall"-->
    <!--      class="adapter-button adapter-button&#45;&#45;green sesui-callform&#45;&#45;button"-->
    <!--      v-on:click.stop="sesuiCallClient.makeVideoCall()"-->
    <!--    >-->
    <!--      Video-->
    <!--    </button>-->

    <span class="adapter-button--separator"></span>
    <!--    v-if="sesuiCallClient.state.ui.showEndCall"-->
    <button
      v-if="false"
      class="adapter-button adapter-button--red sesui-callform--button"
      v-on:click.stop="sesuiCallClient.endCall()"
    >
      End
    </button>

    <div
      v-text="sesuiCallClient.state.message.text"
      :style="sesuiCallClient.state.message.css"
    ></div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  SetupContext,
  watch
} from "@vue/composition-api";
import { sesuiCallClientFactory } from "@/sesui/callform/useSesuiCallForm";
import {
  CleoPermissionName,
  ICleoPermission
} from "@/permissions/permission-models";
import {
  useConfigHelper
} from "@/common/config/config-store";

export default defineComponent({
  name: "sesui-callform",
  components: {},
  props: {
    value: {
      type: String,
      default: () => {
        return "";
      }
    },
    userPermissions: {
      type: Object as PropType<
        Partial<Record<CleoPermissionName, ICleoPermission>>
      >,
      default: () => {
        return {};
      }
    }
  },
  setup(
    props: {
      value: string;
      userPermissions: Partial<Record<CleoPermissionName, ICleoPermission>>;
    },
    context: SetupContext
  ) {
    const sesuiCallClient = sesuiCallClientFactory(props.userPermissions);
    const configHelper = useConfigHelper();

    sesuiCallClient.state.telephoneNumberToCall = props.value;

    watch(
      () => props.value,
      (newValue: string, oldValue: string) => {
        sesuiCallClient.state.telephoneNumberToCall = newValue;
      }
    );

    function onTelephoneNumberChanged() {
      context.emit("input", sesuiCallClient.state.telephoneNumberToCall);
      context.emit("onChanged", sesuiCallClient.state.telephoneNumberToCall);
    }

    function clickEditTelephone() {
      /**
       *
       */
      sesuiCallClient.state.ui.allowTelephoneEdit = !sesuiCallClient.state.ui
        .allowTelephoneEdit;
    }

    return {
      sesuiCallClient,
      onTelephoneNumberChanged,
      clickEditTelephone,
      configHelper
    };
  }
});
</script>

<style>
.sesui-callform--edit-button {
  text-decoration: none;
}
.sesui-callform--button {
  width: 40px;
  height: 25px;
}
</style>
