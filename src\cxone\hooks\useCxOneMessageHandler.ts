import { ref, computed } from "@vue/composition-api";
import { useCxOneController } from "../controllers/useCxOneController";
import { CleoCxOneMessage, CleoCxOneMessageType } from "../models";
import { loggerInstance } from "@/common/Logger";

export interface CxOneMessageHandlerOptions {
  socketUrl: string;
  autoConnect?: boolean;
  onNlpMatched?: (message: CleoCxOneMessage) => Promise<void>;
  onNlpUnmatched?: (message: CleoCxOneMessage) => Promise<void>;
  onNoPdi?: (message: CleoCxOneMessage) => Promise<void>;
  onSmsMatched?: (message: CleoCxOneMessage) => Promise<void>;
}

export function useCxOneMessageHandler(options: CxOneMessageHandlerOptions) {
  const {
    socketUrl,
    autoConnect = true,
    onNlpMatched,
    onNlpUnmatched,
    onNoPdi,
    onSmsMatched,
  } = options;

  // Initialize the controller
  const {
    state,
    socketController,
    initialize,
    processMessage,
  } = useCxOneController(socketUrl);

  // Message history
  const messageHistory = ref<CleoCxOneMessage[]>([]);

  // Custom message handlers
  const handleMessage = async (message: CleoCxOneMessage) => {
    // Add to history
    messageHistory.value.unshift(message);
    if (messageHistory.value.length > 50) {
      messageHistory.value.pop();
    }

    // Process based on message type
    try {
      switch (message.Type) {
        case "NLP_MATCHED":
          if (onNlpMatched) await onNlpMatched(message);
          break;
        case "NLP_UNMATCHED":
          if (onNlpUnmatched) await onNlpUnmatched(message);
          break;
        case "NO_PDI":
          if (onNoPdi) await onNoPdi(message);
          break;
        case "SMS_MATCHED":
          if (onSmsMatched) await onSmsMatched(message);
          break;
        default:
          loggerInstance.log("Unknown CxOne message type:", message.Type);
      }
    } catch (error) {
      loggerInstance.log("Error in custom CxOne message handler:", error);
    }

    // Also process with the default handler
    await processMessage(message);
  };

  // Connect if autoConnect is true
  if (autoConnect) {
    initialize();
    // .catch(error => {
    //   loggerInstance.log("Failed to auto-connect to CxOne socket:", error);
    // });
  }

  // Computed properties
  const isConnected = computed(() => socketController.isConnected);
  const connectionStatus = computed(() => socketController.state.status);

  return {
    state,
    socketController,
    messageHistory,
    isConnected,
    connectionStatus,
    initialize,
    handleMessage,
    sendMessage: processMessage,
  };
}
