import { ICleoServerResponse } from "@/common/cleo-common-models";

const baseHostPath =
  window.MyGlobalSession.Global_DB_Paths.HOST_PATH +
  "/" +
  window.MyGlobalSession.Global_DB_Paths.PATH_CALL;
const baseApiPath = "/(sesui)?openagent";

export function makeCall(
  telephoneNumberToCall: string
): Promise<ICleoServerResponse<string>> {
  const url =
    baseHostPath +
    baseApiPath +
    "&action=MAKE_CALL&TEL_NO=" +
    telephoneNumberToCall;

  return (localCache.getUrlDataWithCache(url, false) as unknown) as Promise<
    ICleoServerResponse<string>
  >;
}
