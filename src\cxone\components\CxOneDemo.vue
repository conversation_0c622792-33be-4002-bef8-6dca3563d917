<template>
  <div class="cxone-demo">
    <h2>CxOne Integration Demo</h2>

    <CxOneConnection :socket-url="socketUrl" />

    <div class="control-section">
      <div class="form-group">
        <label>User Email:</label>
        <input
          type="text"
          v-model="cxOneController.state.myUserIdentifier"
          placeholder="Enter user email"
          class="form-control"
        />
      </div>

      <div class="form-group">
        <label>Mock Message:</label>
        <textarea
          v-model="mockMessage"
          placeholder="Enter JSON message"
          class="form-control mock-textarea"
        ></textarea>
      </div>

      <div class="form-actions">
        <button @click="loadSampleMessage" class="sample-button">
          Load Sample
        </button>
        <button @click="sendMockMessage" class="send-button">
          Send Mock Message
        </button>
      </div>
    </div>

    <div v-if="lastMessage" class="message-display">
      <h3>Last Message</h3>
      <pre>{{ formattedMessage }}</pre>

      <div class="message-type">
        Type: <strong>{{ lastMessage.Type }}</strong>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch } from "@vue/composition-api";
import { useCxOneController } from "../controllers/useCxOneController";
import CxOneConnection from "./CxOneConnection.vue";
import { CleoCxOneMessage } from "../models";

export default defineComponent({
  name: "CxOneDemo",
  components: {
    CxOneConnection
  },
  setup() {
    // Configuration
    const socketUrl = ref(
      process.env.VUE_APP_CXONE_SOCKET_URL ||
        "http://***********:8080/demographicHub"
    );

    // Mock message state
    const mockMessage = ref("");

    // Initialize the controller
    const cxOneController = useCxOneController(socketUrl.value);

    // Initialize on component creation
    cxOneController.initialize();

    // Computed properties for display
    const lastMessage = computed(() => cxOneController.state.lastMessage);

    const formattedMessage = computed(() => {
      if (!lastMessage.value) return "";
      return JSON.stringify(lastMessage.value, null, 2);
    });

    // Function to send mock message
    function sendMockMessage() {
      try {
        const messageObj = JSON.parse(mockMessage.value) as CleoCxOneMessage;
        cxOneController.processMessage(messageObj);
      } catch (error) {
        console.error("Invalid JSON format:", error);
        alert("Invalid JSON format. Please check your input.");
      }
    }

    // Function to load a sample message
    function loadSampleMessage() {
      const sample: CleoCxOneMessage = {
        ContactID: "MOCK-" + Date.now(),
        CaseID: "CASE-12345",
        UserEmail: cxOneController.state.myUserIdentifier || "<EMAIL>",
        Type: "NLP_MATCHED",
        PhoneNumber: "07700900123"
      };
      mockMessage.value = JSON.stringify(sample, null, 2);
    }

    // Watch for new messages
    watch(
      () => cxOneController.state.lastMessage,
      newMessage => {
        if (newMessage) {
          console.log("New CxOne message received:", newMessage);
        }
      }
    );

    return {
      socketUrl,
      lastMessage,
      formattedMessage,
      cxOneController,
      mockMessage,
      sendMockMessage,
      loadSampleMessage
    };
  }
});
</script>

<style scoped>
.cxone-demo {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.control-section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-control {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.mock-textarea {
  height: 120px;
  font-family: monospace;
}

.form-actions {
  display: flex;
  gap: 10px;
}

.send-button,
.sample-button {
  padding: 8px 16px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.sample-button {
  background-color: #2196f3;
}

.message-display {
  margin-top: 20px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f9f9f9;
}

pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow: auto;
  max-height: 300px;
}

.message-type {
  margin-top: 10px;
  font-size: 14px;
}
</style>
