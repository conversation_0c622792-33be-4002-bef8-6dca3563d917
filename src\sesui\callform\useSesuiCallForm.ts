import { broadcastChannelNameSocketMessages } from "@/sesui/useSesui";

import { computed, reactive } from "@vue/composition-api";
import * as SesuiService from "@/sesui/sesui-service";
import * as SesuiApi from "@/sesui/api/sesui-api";
import {
  CleoPermissionName,
  ICleoPermission
} from "@/permissions/permission-models";
import {
  broadcastChannelNameForController,
  IBroadcastActionEndCall,
  IBroadcastActionFilterMessage,
  IBroadcastActionMakeCall,
  ISesuiEvent,
  ISesuiEventMessage,
  ISesuiEventMessageNewCallState,
  ISesuiOpStatusMessage,
  SesuiEventType
} from "@/sesui/use-sesui-models";

export interface ISesuiCallFormState {
  ui: {
    allowTelephoneEdit: boolean;
    showMakeCall: boolean;
    showEndCall: boolean;
  };
  telephoneNumberToCall: string;
  callState: ISesuiEventMessageNewCallState;
  message: {
    text: string;
    css: Record<string, string>;
  };
}

export function sesuiCallClientFactory(
  userPermissions: Partial<Record<CleoPermissionName, ICleoPermission>>
) {
  const state: ISesuiCallFormState = reactive(
    SesuiService.factorySesuiCallFormState()
  );

  /**
   * Messages sent from controller to this client.
   */
  const broadcastChannelSocket = new BroadcastChannel(
    broadcastChannelNameSocketMessages
  );

  /**
   * Send message "down" to the controller, in the webuiDocview.
   */
  const broadcastChannelForController = new BroadcastChannel(
    broadcastChannelNameForController
  );

  broadcastChannelSocket.onmessage = function(webSocketEvent) {
    processWebSocketEvent(webSocketEvent);
  };

  function processWebSocketEvent(webSocketEvent: { data: ISesuiEvent }) {
    const eventType =
      webSocketEvent.data &&
      webSocketEvent.data.event &&
      webSocketEvent.data.event.event_type
        ? webSocketEvent.data.event.event_type
        : "";

    console.log(
      "sesuiCallClientFactory() onmessage eventType: " + eventType,
      webSocketEvent
    );

    /**
     * Message coming up from socket controller have these "action" types...
     */
    const eventTypeMapActions: Record<SesuiEventType, any> = {
      call_new_state: function() {
        callNewState(
          webSocketEvent.data.event as ISesuiEventMessageNewCallState
        );
      },
      call_end: function() {
        endCallState();
      },
      session_start_result: function() {
        return;
      },
      op_status: function() {
        opStatus(webSocketEvent.data.event as ISesuiOpStatusMessage);
      }
    };

    //  If we can match the action type...
    if (
      eventType.length > 0 &&
      eventTypeMapActions[eventType as SesuiEventType]
    ) {
      //  ...then do something.
      eventTypeMapActions[eventType as SesuiEventType]();
    }
  }

  function callNewState(eventData: ISesuiEventMessageNewCallState) {
    state.ui.showMakeCall = false;
    state.ui.showEndCall = true;

    const callStateMap = {
      502: eventData.status_text,
      200: "Connected  " + eventData.status_text,
      116: eventData.status_text
    };

    const message =
      eventData.new_state && callStateMap[eventData.new_state]
        ? callStateMap[eventData.new_state]
        : eventData.status_text;
    simpleMessage(message);
  }

  function endCallState() {
    state.ui.showMakeCall = true;
    state.ui.showEndCall = false;
    simpleMessage("Call Ended");
  }

  function opStatus(eventData: ISesuiOpStatusMessage) {
    // if (eventData.operator_id) {
    //
    // }
    // console.error("opStatus UNDER CONSTRUCTION");
    userMessage({
      message: eventData.op_state_desc,
      timeOut: 0,
      level: "INFO"
    });
    if (eventData.op_state_desc === "Unreachable") {
      endCall();
      endCallState();
    }
  }

  function simpleMessage(message: string) {
    userMessage({
      message: message,
      timeOut: 0,
      level: "INFO"
    });
  }

  function userMessage(messagePayload: {
    message: string;
    timeOut: number;
    level: "INFO" | "WARNING" | "ERROR";
  }) {
    const defaultCss = {
      "font-weight": "600",
      color: "blue",
      padding: "5px 0"
    };
    const textColorMap = {
      INFO: defaultCss,
      WARNING: {
        "font-weight": "600",
        color: "blue",
        padding: "5px 0"
      },
      ERROR: {
        "font-weight": "600",
        color: "blue",
        padding: "5px 0"
      }
    };

    state.message.text = messagePayload.message;
    state.message.css = textColorMap[messagePayload.level]
      ? textColorMap[messagePayload.level]
      : defaultCss;
  }

  function makeCall() {
    //  For now, we are switching to api call.
    makeCallViaServer();
    // simpleMessage("Connecting...");
    // state.ui.showMakeCall = false;
    // const broadcastAction: IBroadcastActionMakeCall = {
    //   action: "MAKE_CALL",
    //   payload: {
    //     telephoneNumber: state.telephoneNumberToCall
    //   }
    // };
    // broadcastChannelForController.postMessage(broadcastAction);
  }

  function makeCallViaServer() {
    SesuiApi.makeCall(state.telephoneNumberToCall)
      .then(resp => {
        console.log("makeCallViaServer() resp", resp);
        if (resp.RESULT === "SUCCESS") {
          simpleMessage(
            "A call has been sent to Sesui, if your Sesui phone does not start ringing, please switch to the Sesui client."
          );
          state.ui.showMakeCall = false;
        } else {
          simpleMessage("Error: " + resp.DATA);
        }
      })
      .finally(() => {
        console.log("makeCallViaServer() finally");
      });
  }

  function makeVideoCall() {
    simpleMessage("Connecting...");
    state.ui.showMakeCall = false;
    // const broadcastAction: IBroadcastActionMakeCall = {
    //   action: "MAKE_CALL",
    //   payload: {
    //     telephoneNumber: state.telephoneNumberToCall
    //   }
    // };
    // broadcastChannelForController.postMessage(broadcastAction);

    // TODO config!!!!!!!!!!!!!!
    const vidoeUrl =
      "https://auto2.call-vision.com/app_launch.asp?u=55721608&p=Ru1593!&email=&number=07912626865&videoless=true";
    window.open(vidoeUrl, "_sesui-video");
  }

  function endCall() {
    const broadcastAction: IBroadcastActionEndCall = {
      action: "END_CALL",
      payload: {
        telephoneNumber: state.telephoneNumberToCall,
        reason: ""
      }
    };
    broadcastChannelForController.postMessage(broadcastAction);
  }

  const isTelephoneNumberValid = computed(() => {
    return SesuiService.isTelephoneNumberValid(state.telephoneNumberToCall);
  });

  return {
    state,
    makeCall,
    makeVideoCall,
    endCall,
    isTelephoneNumberValid
  };
}
