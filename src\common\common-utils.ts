import {
  <PERSON>inoNameAbbrev,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  IsoDateTimeWithOffset
} from "@/common/common-models";

/**
 * N.B. Date objects, Function or Infinity will be lost
 * @param sourceObject
 */
export function simpleObjectClone<T>(sourceObject: T): T {
  if (typeof sourceObject === "undefined" || sourceObject === null) {
    return sourceObject;
  }
  return JSON.parse(JSON.stringify(sourceObject));
}

/**
 * Deep clone an object.  N.B. can be slow, preserves functions.
 * @param obj
 */
export function deepCloneObject<T>(obj: T): T {
  if (obj === null || typeof obj !== "object") return obj; // Return primitives as is

  const clone: any = Array.isArray(obj) ? [] : {};

  Object.keys(obj).forEach(key => {
    const value = (obj as any)[key];
    if (typeof value === "function") {
      clone[key] = value.bind(clone); // Preserve `this` binding
    } else {
      clone[key] = deepCloneObject(value); // Recursively clone objects
    }
  });

  return clone as T;
}

export function findFirst<T>(
  pred: (t: T) => boolean,
  someArray: T[]
): T | null {
  for (let i = 0; i < someArray.length; i++) {
    const someT = someArray[i];
    const res = pred(someT);
    if (res) {
      return someT;
    }
  }
  return null;
}

export function findFirstIndex<T>(
  pred: (t: T) => boolean,
  someArray: T[]
): number | -1 {
  for (let i = 0; i < someArray.length; i++) {
    const someT = someArray[i];
    const res = pred(someT);
    if (res) {
      return i;
    }
  }
  return -1;
}

/**
 * Handles sorting on strings or numbers
 * @param prop
 * @param someArray
 * @param order
 */
export function sortArray<ObjectType, PropName extends keyof ObjectType>(
  prop: ((t: ObjectType) => string) | PropName,
  someArray: ObjectType[],
  order: "ASC" | "DESC" = "ASC"
): ObjectType[] {
  if (!someArray || someArray.length === 0) {
    return someArray;
  }

  return someArray.sort((a, b) => {
    const propValueA: unknown = typeof prop === "function" ? prop(a) : a[prop];
    const propValueB: unknown = typeof prop === "function" ? prop(b) : b[prop];

    if (typeof propValueA === "string" && typeof propValueB === "string") {
      const compA: string = (order === "ASC"
        ? propValueA
        : propValueB
      ).toUpperCase();
      const compB: string = (order === "ASC"
        ? propValueB
        : propValueA
      ).toUpperCase();
      // const compB: string = propValueB.toString().toUpperCase();

      if (compA < compB) {
        return -1;
      }
      if (compA > compB) {
        return 1;
      }
      // names must be equal
      return 0;
    }

    if (typeof propValueA === "number" && typeof propValueB === "number") {
      return order === "ASC"
        ? propValueA - propValueB
        : propValueB - propValueA;
    }

    return 0;
  });
}

export function isOnlyNumbers(
  someValue: string,
  allowRealNumberChars = true
): boolean {
  if (allowRealNumberChars) {
    return /^(?:-(?:[1-9](?:\d{0,2}(?:,\d{3})+|\d*))|(?:0|(?:[1-9](?:\d{0,2}(?:,\d{3})+|\d*))))(?:.\d+|)$/.test(
      someValue
    );
  }
  return /^\d+$/.test(someValue);
}

export function isEmpty(value: string | Array<unknown>): boolean {
  if (typeof value === "string") {
    return isEmptyString(value);
  }
  if (Array.isArray(value)) {
    return isEmptyArray(value);
  }
  return true;
}

export function isEmptyString(value: string): boolean {
  return value.replace(/ /g, "").length === 0;
}

export function isEmptyArray(value: Array<unknown>): boolean {
  return value.length === 0;
}

export function isMobileNumber(phoneNumber: string) {
  phoneNumber = phoneNumber.replace(/ /g, "");
  const pattern = /^(\+44\s?7\d{3}|\(?07\d{3}\)?)\s?\d{3}\s?\d{3}$/;
  return pattern.test(phoneNumber);
}

export function isTelephoneNumber(phoneNumber: string) {
  const pattern = /^\s*((?:[+](?:\s?\d)(?:[-\s]?\d)|0)?(?:\s?\d)(?:[-\s]?\d){9}|[(](?:\s?\d)(?:[-\s]?\d)+\s*[)](?:[-\s]?\d)+)\s*$/;
  return pattern.test(phoneNumber);
}

export function toIsoString(date: Date): IsoDateTimeWithOffset {
  const tzo = -date.getTimezoneOffset();
  const dif = tzo >= 0 ? "+" : "-";
  function pad(num: number): string {
    return (num < 10 ? "0" : "") + num;
  }

  return (
    date.getFullYear() +
    "-" +
    pad(date.getMonth() + 1) +
    "-" +
    pad(date.getDate()) +
    "T" +
    pad(date.getHours()) +
    ":" +
    pad(date.getMinutes()) +
    ":" +
    pad(date.getSeconds()) +
    dif +
    pad(Math.floor(Math.abs(tzo) / 60)) +
    ":" +
    pad(Math.abs(tzo) % 60)
  );
}

export function convertArrayToObject<
  ObjectType,
  PropName extends keyof ObjectType
>(
  prop: ((t: ObjectType) => string) | PropName,
  someArray: ObjectType[]
): Record<string, ObjectType> {
  if (!someArray || someArray.length === 0) {
    return {};
  }
  return someArray.reduce((accum, obj: ObjectType) => {
    const propValue: string = (((typeof prop === "function"
      ? prop(obj)
      : obj[prop]) as any) as string).toString();

    accum[propValue] = obj;
    return accum;
  }, {} as Record<string, ObjectType>);
}

// create a function that uses Temporal to create an ISO string with local offset, e.g. 2023-10-03T12:00:00+01:00
// export function toIsoStringWithLocalOffset(date: Date): IsoDateTimeWithOffset {
//   const temporalDate = Temporal.PlainDateTime.from(date);
//   const temporalZonedDate = temporalDate.withTimeZone("local");
//   return temporalZonedDate.toString();
// }

// create a function that does not use Temporal to create an ISO string with local offset, e.g. 2023-10-03T12:00:00+01:00
export function toIsoStringWithLocalOffsetWithoutTemporal(
  date: Date
): IsoDateTimeWithOffset {
  // Get timezone offset for the specific date
  const targetDate = new Date(date.getTime());
  const offset = -targetDate.getTimezoneOffset();
  const sign = offset >= 0 ? "+" : "-";
  const hours = Math.floor(Math.abs(offset / 60));
  const minutes = Math.abs(offset % 60);

  // If in BST (offset is 60 minutes), add an hour to the time
  const adjustedDate = new Date(targetDate);
  if (offset === 60) {
    adjustedDate.setHours(adjustedDate.getHours() + 1);
  }

  return (
    adjustedDate.toISOString().slice(0, 19) +
    sign +
    String(hours).padStart(2, "0") +
    ":" +
    String(minutes).padStart(2, "0")
  );
}

// create a function that turns 07/10/2024 14:07:03 into local ISO datetime  with offset, e.g. 2024-10-07T14:07:03+01:00
// Use Date object and methods to achieve this.  Do NOT create a UTC datetime.  Name the function that best describes the incoming pattern.
export function toLocalISOWithOffset(
  dateString: string
): {
  iso: IsoDateTimeWithOffset;
  error: string;
} {
  try {
    if (!dateString) {
      return {
        iso: "",
        error: "dateString is undefined"
      };
    }

    // if empty string, return empty string
    if (dateString === "") {
      return {
        iso: "",
        error: "dateString is empty"
      };
    }

    // assume the string is already in ISO format, return it as is
    if (dateString.includes("T")) {
      return {
        iso: dateString,
        error: ""
      };
    }

    // check dateString is in the format DD/MM/YYYY HH:MM:SS
    if (!dateString.match(/^\d{2}\/\d{2}\/\d{4} \d{2}:\d{2}:\d{2}$/)) {
      //  check the date portion is valid
      if (!dateString.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
        console.log(
          "toLocalISOWithOffset() dateString: " +
            dateString +
            " is not in the format DD/MM/YYYY"
        );
        // return dateString + " date not in format DD/MM/YYYY";
        return {
          iso: "",
          error: "date not in format DD/MM/YYYY: " + dateString
        };
      }

      //  check the time portion is valid
      if (!dateString.match(/\d{2}:\d{2}:\d{2}$/)) {
        console.error(
          "toLocalISOWithOffset() dateString: " +
            dateString +
            " is not in the format HH:MM:SS"
        );
        return {
          iso: "",
          error: "time not in format HH:MM:SS: " + dateString
        };
      }

      console.log(
        "toLocalISOWithOffset() dateString: " +
          dateString +
          " is not in the format DD/MM/YYYY HH:MM:SS"
      );
      return {
        iso: "",
        error: "date not in format DD/MM/YYYY HH:MM:SS: " + dateString
      };
    }

    // debugger;
    // the inbound pattern is 07/10/2024 14:07:03, it is NOT US pattern but UK pattern, use slice to get the date and time parts
    const day = dateString.slice(0, 2);
    const month = dateString.slice(3, 5);
    const year = dateString.slice(6, 10);
    const time = dateString.slice(11, 19);

    // create a date object from the date and time parts
    const date = new Date(year + "-" + month + "-" + day + "T" + time);
    // return toIsoStringWithLocalOffsetWithoutTemporal(date);
    return {
      iso: toIsoStringWithLocalOffsetWithoutTemporal(date),
      error: ""
    };
  } catch (error) {
    console.error("toLocalISOWithOffset() dateString: " + dateString, error);
    console.error(error);
    return {
      iso: "",
      error: error.message
    };
  }
}

/**
 * Specific to Cleo Question Constants, e.g. OUT_OF_HOURS_PROFESSIONAL_LINE, MENTAL_HEALTH, PAEDIATRICS, etc.
 * @param str
 */
export function properCaseQuestionConstant(str: string): string {
  return str
    .split("_")
    .map(function(word) {
      if (word.toUpperCase() === word) {
        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
      }
      return word;
    })
    .join(" ");
}

/**
 * Formats a user's Domino name based on the specified name format.
 *
 * @param {DominoNameAbbrev | DominoNameFull} userName - The Domino name to be formatted. Can be in abbreviated or full form.
 * @param {"CN" | "ABBREV"} nameFormat - The desired format of the name. "CN" extracts the common name, while "ABBREV" removes unnecessary prefixes.
 * @return {string} The formatted Domino name as per the specified format. Returns an empty string if no `userName` is provided.
 */
export function formatUserDominoName(
  userName: DominoNameAbbrev | DominoNameFull,
  nameFormat: "CN" | "ABBREV" = "CN"
): string {
  if (!userName) {
    return "";
  }

  const hasSeparator = (un: string) => {
    return un.indexOf("/");
  };

  const formatter: Record<typeof nameFormat, (un: string) => string> = {
    CN: (un: string) => {
      //  E.g. CN=Joe Bloggs/O=sehnp
      let tmp = un;
      if (hasSeparator(un)) {
        tmp = un.split("/")[0];
      }
      return tmp.replace("CN=", "");
    },
    ABBREV: (un: string) => {
      return un.replace("CN=", "").replace("O=", "");
    }
  };
  return formatter[nameFormat] ? formatter[nameFormat](userName) : userName;
}
