import {
  CLEO_CLIENT_SERVICE,
  <PERSON><PERSON><PERSON>,
  GENDER_ID,
  IsoDateTimeWithOffset
} from "@/common/common-models";
import {
  GoodSamStatus,
  TextSimpleZeroOne
} from "@/calls/grids/grids-named/legacy/models/grid-legacy-models";
import { BrisDocSupportType } from "@/calls/details/complete/brisdoc/ui/non-clinical-and-prescribing/brisdoc-non-clinical-and-prescribing-models";

export interface IObjectBase {
  Id: number | null;
  Description: string | null;
}

export interface IObjectConcrete extends IObjectBase {
  Id: number;
  Description: string;
}

// TODO - Move to common/common-models.ts
export interface ICleoService extends IObjectBase {
  Type: "" | "OOH" | "111" | "CAS" | null;
}

// CallDobIso
// CallServiceOriginal
// CaseWarmTransferFail
// ComfortCallResponse

export type CallStatusValue = 9 | 1 | 2;

export interface ICleoCallSummary {
  AllViewExclude: null | "0" | "1";
  AllViewInclude:
    | null
    | ""
    | "DX500"
    | "KMS_SEACAMB"
    | "ED"
    | "UTC"
    | "FCMS"
    | "OVERSIGHT_FOLLOW_UP";

  CallNo: number;
  CallService: ICleoService;
  IucContract: ICleoService;
  cleoClientService: CLEO_CLIENT_SERVICE;
  CallPostCode: string;
  // CallMf: GENDER;
  CallGenderId: GENDER_ID;
  CallNhsNo: string | null;

  BreachActualTime: string | null;
  BreachWarnActualTime: string | null;
  BreachPreActualTime: string | null;
  BreachPriority: number | null; //   from Breach config.
  BreachLevel1Mins: number | null;
  // BreachKey: string; //  Needs to go.
  ApplyBreach: boolean;

  CallWithBaseAckTime: string | null;
  CallUrgentYn: boolean;
  CallReceivedTime: string; //  ISO date-time with offset
  // CallReceivedISO: string; //  ...coming thru null.

  CallStatusValue: CallStatusValue;
  IsLocked: string; //  User who has call locked

  CallForename: string;
  CallSurname: string;

  Itk111Online: boolean;

  ChFinalDispositionCode: string | null;
  ChFinalDispositionDescription: string | null;

  FinalDispositionCode: string | null;
  FinalDispositionDescription: string;

  DispatchVehicle: string;
  WalkIn: boolean;

  CallCallback: number; //  Number of times patient called IC24
  CallWarmTransferred: null | true | false;
  PdsTracedAndVerified: boolean;
  PDSAdminTrace: "NO_MATCH" | "TOO_MANY_MATCHES" | string;

  CallAddress1: string;
  CallAddress2: string;
  CallAddress3: string;
  CallAddress4: string;
  CallTown: string;

  CallDobIso: string | null;
  CallAge: number;
  CallAgeClass: string;

  CallCName: string;
  CallCRel: string;

  CallCreatedBy: string;
  CallArrivedTime: string | null;

  CallDoctorName: string;

  CallClassification: IObjectBase;
  CallSubClassification: IObjectBase;

  CallTelNo: string;
  CallTelNoAlt1: string;
  CallTelNoAltType1: string;
  CallTelNo_R: string;

  CallSymptoms: string;

  Call1StContact: string | null;
  Call1StContactPathways: string | null;
  PathwaysCaseId: string | null;

  PatientContactCode: string;
  PatientContactCodeCount: number;
  LastFailedContactTime?: string | null; //  date-time

  CallAppointmentTime: string | null;
  AFT_UNABLE_REASON?: string; //  DELETE

  ComfortSentServiceTime: string | null; //  OUTBOUND - CLEO sends request to MessageBird
  ComfortSmsTime?: string; //  INBOUND  - MessageBird sent SMS at this time
  ComfortSentService2Time: string | null; //  OUTBOUND 2 - CLEO sends request to MessageBird
  ComfortSmsTime2: string | null; //  INBOUND 2  - MessageBird sent SMS at this time

  CourtesyTime: string; //  "2020-11-09T20:52:42"
  CourtesyUser: string | null;
  CourtesyCount: number;
  CourtesyContact: boolean;

  CliniHighPriority: boolean;

  //  TODO Adapter not sending.
  CallInformationalOutcomes: string;
  CallInformationalSubOutcomes: string;
  CallInformationalOutcomesComment: string;

  CallPractice: string;

  //  New/unmapped fields
  // CaseCallBackRequired: "False"

  DutyBase: string | null;
  StartConsultationPerformed: boolean;

  Long?: number;
  Lat?: number;

  CasValidationCount: number;
  CasValidationUser: string;
  CasValidationTime: string;
  CasValidationReason: string;

  CaseComments: string;
  LinkedCallID: string;

  COMPLETE_PREVENT: "1" | "0" | "";

  FOLLOW_UP_URGENT: "1" | "0" | "";
  FOLLOW_UP_Active: string | null; // E.g. "17/03/2025 08:35:00" leaving as this format for now.
  Call_HCP: "1" | "0" | "";

  OversightValidationType:
    | ""
    | "Approve"
    | "Clinical Co-Ordinator Review in progress"
    | "Further Clinical Input Required";

  OVERSIGHT_BASE_TRIAGE_TYPE: string | "Isolation" | "Does patient need PPE"; // etc. from Keywords, putting her as an example.

  SMS_HAS: boolean;
  SMS_SENT: boolean;
  SMS_LATEST_AT: IsoDateTimeWithOffset | "";
  SMS_LATEST_USER: string;
  SMS_LATEST_MESSAGE: string;

  Cpl_supportTypeRequired: BrisDocSupportType | "";
  GOODSAM_IMAGE_STATUS: GoodSamStatus;

  CallFAction: string;
  Cpl_furtherActionGPText: string;

  PLS_REASON: string;
  PLS_TIME: string;
  PLS_USER: string;
}

//  TEST_COL: "TEST_COL",
export const CLEO_GRID_COLUMN_NAMES = {
  LOCKED: "LOCKED",
  CALLBACK: "CALLBACK",
  WALK_IN: "WALK_IN",
  WARM_TRANSFERRED: "WARM_TRANSFERRED",
  DX: "DX",
  BREACH: "BREACH",
  FOLLOW_UP_Active: "FOLLOW_UP_Active",
  BREACH_LEVEL_HUMAN: "BREACH_LEVEL_HUMAN",
  CALL_NO: "CALL_NO",
  PDS: "PDS",
  SERVICE: "SERVICE",
  PATIENT: "PATIENT",
  PATIENT_AGE: "PATIENT_AGE",
  TOWN: "TOWN",
  RELATIONSHIP: "RELATIONSHIP",
  TEL_ALT: "TEL_ALT",
  FIRST_CONTACT: "FIRST_CONTACT",
  ASSIGNED_TO: "ASSIGNED_TO",
  RECEIVED: "RECEIVED",
  APPOINTMENT_TIME: "APPOINTMENT_TIME",
  ARRIVED: "ARRIVED",
  CLASSIFICATION: "CLASSIFICATION",
  COMFORT_CALL: "COMFORT_CALL",
  MORE_INFO: "MORE_INFO",
  CAS_VALIDATION: "CAS_VALIDATION",
  CASE_COMMENTS: "CASE_COMMENTS",
  CALL_HCP: "CALL_HCP",
  SMS: "SMS",
  GOODSAM_IMAGE_STATUS: "GOODSAM_IMAGE_STATUS",
  FURTHER_ACTION: "FURTHER_ACTION",
  FURTHER_ACTION_GP: "FURTHER_ACTION_GP",
  CALL_PRACTICE: "CALL_PRACTICE",
  DOB: "DOB",
  NHS_NO: "NHS_NO",
  PLS: "PLS"
};

export type CLEO_GRID_COLUMN_NAME = keyof typeof CLEO_GRID_COLUMN_NAMES;

export interface ISimpleTrigger<T> {
  timeIso: string;
  data?: T;
}

export interface ISimpleTriggerData<T> extends ISimpleTrigger<T> {
  data: T;
}
